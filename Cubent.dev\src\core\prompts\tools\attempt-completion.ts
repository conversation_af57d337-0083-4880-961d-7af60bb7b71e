export function getAttemptCompletionDescription(): string {
	return `## attempt_completion
Description: This tool signals task completion and ends the current task. It is NOT used to communicate with or show anything to the user.

CRITICAL: This tool ENDS the task. Only use it after you communicate, explain, or show content to the user.

For informational requests like "what is real estate" or explanations, respond directly in conversation without using any tools.

Use this tool ONLY when:
1. You have used other tools to accomplish actual work (file edits, code changes, commands, etc.)
2. You have received confirmation from the user that those tool uses were successful
3. You need to end the task

IMPORTANT NOTE: This tool CANNOT be used until you've confirmed from the user that any previous tool uses were successful. Failure to do so will result in code corruption and system failure. Before using this tool, you must ask yourself in <thinking></thinking> tags if you've confirmed from the user that any previous tool uses were successful. If not, then DO NOT use this tool.
Parameters:
- result: (required) Internal completion marker. This is NOT shown to the user. Only use this after actual work has been completed with other tools.
- command: (optional) A CLI command to execute after task completion. For example, use \`open index.html\` to open a created html website, or \`open localhost:3000\` to open a locally running development server. But DO NOT use commands like \`echo\` or \`cat\` that merely print text. This command should be valid for the current operating system. Ensure the command is properly formatted and does not contain any harmful instructions.
Usage:
<attempt_completion>
<result>
Task completion marker
</result>
<command>Command to execute after completion (optional)</command>
</attempt_completion>

Example: Requesting to attempt completion after completing actual work
<attempt_completion>
<result>
Task completed
</result>
<command>open index.html</command>
</attempt_completion>`
}
