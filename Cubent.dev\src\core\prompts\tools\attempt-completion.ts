export function getAttemptCompletionDescription(): string {
	return `## attempt_completion
Description: Signals that the task is finished after all required tools have been executed. This tool is NOT for showing messages or summaries to the user.
IMPORTANT NOTE: This tool CANNOT be used until you've confirmed from the user that any previous tool uses were successful. Failure to do so will result in code corruption and system failure. Before using this tool, you must ask yourself in <thinking></thinking> tags if you've confirmed from the user that any previous tool uses were successful. If not, then DO NOT use this tool.
Parameters:
- result: (required) Internal completion marker **NOT** shown to the user. Leave the tag empty.
- command: (optional) A CLI command to execute to show a live demo of the result. For example, use \`open index.html\` to display a created html website, or \`open localhost:3000\` to display a locally running development server. But DO NOT use commands like \`echo\` or \`cat\` that merely print text. This command should be valid for the current operating system. Ensure the command is properly formatted and does not contain any harmful instructions.
Usage:
<attempt_completion>
<result></result>
<command>Command to demonstrate result (optional)</command>
</attempt_completion>

Example: Requesting to attempt completion with a result and command
<attempt_completion>
<result></result>
<command>open index.html</command>
</attempt_completion>`
}